/**
 * Authentication Service - Business logic layer for authentication
 */
import { apiClient, API_ENDPOINTS, withErrorHandling } from '../api/index.js';
import { encryptPassword } from '../utils/crypto.js';

class AuthService {
  constructor() {
    this.apiClient = apiClient;
  }

  /**
   * Send email verification for signup
   * @param {string} email - User email address
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async sendEmailVerification(email, recaptchaToken) {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.emailVerification, {
          email,
          purpose: 'sign_up',
          recaptchaToken
        });
        return response;
      },
      { operation: 'sendEmailVerification', email }
    );
  }

  /**
   * Verify OTP code (legacy method - kept for backward compatibility)
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @returns {Promise} Service response
   */
  async verifyOTP(email, otp) {
    return await withError<PERSON>andling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.verifyOTP, {
          email,
          otp
        });
        return response;
      },
      { operation: 'verifyOTP', email }
    );
  }

  /**
   * Validate OTP code with token and purpose
   * @param {string} email - User email address
   * @param {string} otp - OTP code
   * @param {string} token - Verification token
   * @param {string} purpose - Purpose of OTP validation (default: 'sign_up')
   * @returns {Promise} Service response
   */
  async validateOTP(email, otp, token, purpose = 'sign_up') {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.validateOTP, {
          email,
          otp,
          token,
          purpose
        });
        return response;
      },
      { operation: 'validateOTP', email, purpose }
    );
  }

  /**
   * Set password for user account
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @param {string} token - Token from OTP validation response (optional for backward compatibility)
   * @returns {Promise} Service response
   */
  async setPassword(email, password, confirmPassword, token = null) {
    // Validate passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match'
      };
    }

    return await withErrorHandling(
      async () => {
        // Encrypt password and use for both fields since they should be identical
        const encryptedPassword = await encryptPassword(password);

        const payload = {
          email,
          password: encryptedPassword,
          confirmPassword: encryptedPassword // Use same encrypted value
        };

        // Add token to payload if provided
        if (token) {
          payload.token = token;
        }

        const response = await this.apiClient.post(API_ENDPOINTS.auth.setPassword, payload);
        return response;
      },
      { operation: 'setPassword', email, hasToken: !!token }
    );
  }

  /**
   * Complete user signup with user details
   * @param {string} email - User email address
   * @param {string} branchId - Branch UUID
   * @param {string} semesterId - Semester UUID
   * @param {string} universityId - University UUID
   * @param {string} roleId - Role UUID
   * @returns {Promise} Service response
   */
  async signup(email, branchId, semesterId, universityId, roleId) {
    return await withErrorHandling(
      async () => {
        const payload = {
          email,
          branchId,
          semesterId,
          universityId,
          roleId
        };

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signup, payload);
        return response;
      },
      { operation: 'signup', email }
    );
  }

  /**
   * Create user account (legacy method - kept for backward compatibility)
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} confirmPassword - Password confirmation
   * @returns {Promise} Service response
   */
  async createAccount(email, password, confirmPassword) {
    // Validate passwords match
    if (password !== confirmPassword) {
      return {
        success: false,
        error: 'Passwords do not match'
      };
    }

    return await withErrorHandling(
      async () => {
        // Encrypt password and use for both fields since they should be identical
        const encryptedPassword = await encryptPassword(password);

        const response = await this.apiClient.post(API_ENDPOINTS.auth.createAccount, {
          email,
          password: encryptedPassword,
          confirmPassword: encryptedPassword // Use same encrypted value
        });
        return response;
      },
      { operation: 'createAccount', email }
    );
  }

  /**
   * Sign in user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async signin(email, password, recaptchaToken) {
    const result = await withErrorHandling(
      async () => {
        // Encrypt password before sending to API
        const encryptedPassword = await encryptPassword(password);

        const response = await this.apiClient.post(API_ENDPOINTS.auth.signin, {
          email,
          password: encryptedPassword,
          recaptchaToken
        });

        // Store auth token if provided
        if (response.token) {
          await this.apiClient.setAuthToken(response.token);
        }

        return response;
      },
      { operation: 'signin', email }
    );

    return result;
  }

  /**
   * Login user
   * @param {string} email - User email address
   * @param {string} password - User password
   * @param {string} recaptchaToken - ReCAPTCHA token
   * @returns {Promise} Service response
   */
  async login(email, password, recaptchaToken) {
    const result = await withErrorHandling(
      async () => {
        // Encrypt password before sending to API
        const encryptedPassword = await encryptPassword(password);

        const response = await this.apiClient.post(API_ENDPOINTS.auth.login, {
          email,
          password: encryptedPassword,
          recaptchaToken
        });

        // Store auth token if provided
        if (response.token) {
          await this.apiClient.setAuthToken(response.token);
        }

        return response;
      },
      { operation: 'login', email }
    );

    return result;
  }

  /**
   * Logout user
   * @returns {Promise} Service response
   */
  async logout() {
    const result = await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.logout);
        return response;
      },
      { operation: 'logout' }
    );

    // Clear auth token regardless of API call result
    await this.apiClient.setAuthToken(null);

    // If API call failed but we still want to clear local state
    if (!result.success) {
      return {
        success: true,
        data: { message: 'Logged out locally' }
      };
    }

    return result;
  }

  /**
   * Resend email verification
   * @param {string} email - User email address
   * @returns {Promise} Service response
   */
  async resendEmailVerification(email) {
    return await withErrorHandling(
      async () => {
        const response = await this.apiClient.post(API_ENDPOINTS.auth.resendVerification, {
          email,
          purpose: 'sign_up'
        });
        return response;
      },
      { operation: 'resendEmailVerification', email }
    );
  }
}

export default AuthService;
