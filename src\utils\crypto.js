/**
 * Hash password using SHA-256 (deterministic - same password always produces same hash)
 * @param {string} password - Plain text password
 * @returns {Promise<string>} SHA-256 hash of the password in hex format
 */
export async function hashPasswordSHA256(password) {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);

    // Convert <PERSON>rray<PERSON>uffer to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
  } catch (error) {
    console.error('SHA-256 password hashing failed:', error);
    throw new Error('Failed to hash password with SHA-256');
  }
}