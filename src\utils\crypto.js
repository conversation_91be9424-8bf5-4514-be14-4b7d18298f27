/**
 * Cryptographic utilities for password encryption and security
 * Uses Web Crypto API for secure client-side encryption
 */

/**
 * Convert string to <PERSON>rrayBuffer
 * @param {string} str - String to convert
 * @returns {ArrayBuffer} ArrayBuffer representation
 */
function stringToArrayBuffer(str) {
  const encoder = new TextEncoder();
  return encoder.encode(str);
}

/**
 * Convert ArrayBuffer to hex string
 * @param {ArrayBuffer} buffer - ArrayBuffer to convert
 * @returns {string} Hex string representation
 */
function arrayBufferToHex(buffer) {
  const byteArray = new Uint8Array(buffer);
  const hexCodes = [...byteArray].map(value => {
    const hexCode = value.toString(16);
    const paddedHexCode = hexCode.padStart(2, '0');
    return paddedHexCode;
  });
  return hexCodes.join('');
}

/**
 * Generate a secure random salt
 * @param {number} length - Length of salt in bytes (default: 16)
 * @returns {string} Hex-encoded salt
 */
export function generateSalt(length = 16) {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return arrayBufferToHex(array);
}

/**
 * Hash password using SHA-256 with salt
 * @param {string} password - Plain text password
 * @param {string} salt - Salt for hashing (optional, will generate if not provided)
 * @returns {Promise<{hash: string, salt: string}>} Hashed password and salt
 */
export async function hashPassword(password, salt = null) {
  try {
    // Generate salt if not provided
    if (!salt) {
      salt = generateSalt();
    }

    // Combine password and salt
    const saltedPassword = password + salt;

    // Convert to ArrayBuffer
    const data = stringToArrayBuffer(saltedPassword);

    // Hash using SHA-256
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);

    // Convert to hex string
    const hash = arrayBufferToHex(hashBuffer);

    return {
      hash,
      salt
    };
  } catch (error) {
    console.error('Password hashing failed:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Hash password using SHA-256 (deterministic - same password always produces same hash)
 * @param {string} password - Plain text password
 * @returns {Promise<string>} SHA-256 hash of the password in hex format
 */
export async function hashPasswordSHA256(password) {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    return arrayBufferToHex(hashBuffer);
  } catch (error) {
    console.error('SHA-256 password hashing failed:', error);
    throw new Error('Failed to hash password with SHA-256');
  }
}

/**
 * Encrypt password using AES-GCM with fixed key for API transmission
 * This matches the backend's expected encryption format
 * @param {string} password - Plain text password
 * @param {string} keyStr - Encryption key (default: "somayyaacademy")
 * @returns {Promise<string>} Encrypted password in format "iv:ciphertext"
 */
export async function encryptPassword(password, keyStr = "somayyaacademy") {
  try {
    const enc = new TextEncoder();

    // 1. Hash the key using SHA-256
    const keyBuffer = await crypto.subtle.digest("SHA-256", enc.encode(keyStr));
    const aesKey = keyBuffer.slice(0, 16); // First 16 bytes (128-bit key)

    // 2. Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", aesKey, "AES-GCM", false, ["encrypt"]);

    // 3. Generate 12-byte IV
    const iv = crypto.getRandomValues(new Uint8Array(12));

    // 4. Encrypt
    const encrypted = await crypto.subtle.encrypt(
        { name: "AES-GCM", iv },
        cryptoKey,
        enc.encode(password)
    );

    // 5. Encode to base64
    const ivBase64 = btoa(String.fromCharCode(...iv));
    const cipherBase64 = btoa(String.fromCharCode(...new Uint8Array(encrypted)));

    return `${ivBase64}:${cipherBase64}`;
  } catch (error) {
    console.error('Password encryption failed:', error);
    throw new Error('Failed to encrypt password');
  }
}

/**
 * Decrypt password using AES-GCM with fixed key (for testing purposes)
 * @param {string} encryptedPassword - Encrypted password in format "iv:ciphertext"
 * @param {string} keyStr - Decryption key (default: "somayyaacademy")
 * @returns {Promise<string>} Decrypted password
 */
export async function decryptPassword(encryptedPassword, keyStr = "somayyaacademy") {
  try {
    const enc = new TextEncoder();
    const dec = new TextDecoder();

    // 1. Split IV and ciphertext
    const [ivBase64, cipherBase64] = encryptedPassword.split(':');
    if (!ivBase64 || !cipherBase64) {
      throw new Error('Invalid encrypted password format');
    }

    // 2. Decode from base64
    const iv = new Uint8Array(atob(ivBase64).split('').map(c => c.charCodeAt(0)));
    const ciphertext = new Uint8Array(atob(cipherBase64).split('').map(c => c.charCodeAt(0)));

    // 3. Hash the key using SHA-256
    const keyBuffer = await crypto.subtle.digest("SHA-256", enc.encode(keyStr));
    const aesKey = keyBuffer.slice(0, 16); // First 16 bytes (128-bit key)

    // 4. Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", aesKey, "AES-GCM", false, ["decrypt"]);

    // 5. Decrypt
    const decrypted = await crypto.subtle.decrypt(
        { name: "AES-GCM", iv },
        cryptoKey,
        ciphertext
    );

    return dec.decode(decrypted);
  } catch (error) {
    console.error('Password decryption failed:', error);
    throw new Error('Failed to decrypt password');
  }
}

/**
 * Verify password against hash
 * @param {string} password - Plain text password to verify
 * @param {string} hash - Stored hash to verify against
 * @param {string} salt - Salt used for original hash
 * @returns {Promise<boolean>} True if password matches
 */
export async function verifyPassword(password, hash, salt) {
  try {
    const result = await hashPassword(password, salt);
    return result.hash === hash;
  } catch (error) {
    console.error('Password verification failed:', error);
    return false;
  }
}

/**
 * Generate a secure random token
 * @param {number} length - Length in bytes (default: 32)
 * @returns {string} Hex-encoded random token
 */
export function generateSecureToken(length = 32) {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return arrayBufferToHex(array);
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with strength indicators
 */
export function validatePasswordStrength(password) {
  const minLength = password.length >= 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const score = [minLength, hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar]
    .filter(Boolean).length;

  let strength = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';

  return {
    minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar,
    score,
    strength,
    isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
  };
}
