import { useState, useEffect } from "react";
import { useLocation, useNavigate } from 'react-router-dom';
import AnimationBox from '../components/common/AnimationBox';
import Header from '../components/layout/Header';
import { initializeServices } from '../services/index.js';
import '../styles/UserDetails.css';

const UserDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || '';

  const [formData, setFormData] = useState({
    university: '',
    branch: '',
    semester: ''
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
      }
    };

    initServices();
  }, []);

  // Redirect if no email is provided
  useEffect(() => {
    if (!email) {
      console.warn('No email provided, redirecting to signup');
      navigate('/signup');
    }
  }, [email, navigate]);

  // Sample data for dropdowns with UUIDs - replace with actual API calls
  const universities = [
    { value: '', label: 'Select University' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa1', label: 'University of Mumbai' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa2', label: 'University of Delhi' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa3', label: 'University of Pune' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa4', label: 'Bangalore University' },
  ];

  const branches = [
    { value: '', label: 'Select Branch' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb1', label: 'Computer Science Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb2', label: 'Information Technology' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb3', label: 'Electronics & Communication' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb4', label: 'Mechanical Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb5', label: 'Civil Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb6', label: 'Electrical Engineering' },
  ];

  const semesters = [
    { value: '', label: 'Select Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc1', label: '1st Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc2', label: '2nd Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc3', label: '3rd Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc4', label: '4th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc5', label: '5th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc6', label: '6th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc7', label: '7th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc8', label: '8th Semester' }
  ];

  // Default role ID for students
  const DEFAULT_ROLE_ID = '3fa85f64-5717-4562-b3fc-2c963f66afd1';

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.university) {
      newErrors.university = 'Please select a university';
    }

    if (!formData.branch) {
      newErrors.branch = 'Please select a branch';
    }

    if (!formData.semester) {
      newErrors.semester = 'Please select a semester';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (e) => {
    e.preventDefault();

    if (!validateForm() || !authService) {
      return;
    }

    setIsLoading(true);

    try {
      // Call the signup API with the required payload
      const result = await authService.signup(
        email,
        formData.branch,        // branchId
        formData.semester,      // semesterId
        formData.university,    // universityId
        DEFAULT_ROLE_ID         // roleId (default student role)
      );

      if (result.success) {
        console.log('✅ Signup completed successfully');
        alert('User details saved successfully! Your account is now complete.');

        // Navigate to dashboard or login page
        navigate('/dashboard', {
          state: {
            message: 'Account setup completed successfully!',
            email
          }
        });
      } else {
        console.error('❌ Signup failed:', result.error);
        alert(`Failed to save user details: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Signup error:', error);
      alert('An error occurred while saving user details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="user-details-container">
      <Header />

      <div className="user-details-content">
        <AnimationBox className="user-details-box">
          <h2 className="h2 user-details-title">User Details</h2>

          <form onSubmit={handleSave} className="user-details-form">

            {/* University Dropdown */}
            <div className="form-group">
              <label htmlFor="university" className="body2-bold">University</label>
              <select
                id="university"
                value={formData.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={`dropdown-select ${errors.university ? 'error' : ''}`}
              >
                {universities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.university && <div className="error-message body4">{errors.university}</div>}
            </div>

            {/* Branch Dropdown */}
            <div className="form-group">
              <label htmlFor="branch" className="body2-bold">Branch</label>
              <select
                id="branch"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                className={`dropdown-select ${errors.branch ? 'error' : ''}`}
              >
                {branches.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.branch && <div className="error-message body4">{errors.branch}</div>}
            </div>

            {/* Semester Dropdown */}
            <div className="form-group">
              <label htmlFor="semester" className="body2-bold">Semester</label>
              <select
                id="semester"
                value={formData.semester}
                onChange={(e) => handleInputChange('semester', e.target.value)}
                className={`dropdown-select ${errors.semester ? 'error' : ''}`}
              >
                {semesters.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.semester && <div className="error-message body4">{errors.semester}</div>}
            </div>

            {/* Save Button */}
            <div className="save-button-container">
              <button
                type="submit"
                className="save-button body2-bold"
                disabled={isLoading || !email}
              >
                {isLoading ? 'Saving...' : 'Complete Signup'}
              </button>
            </div>

            {/* Show email for confirmation */}
            {email && (
              <div className="email-confirmation">
                <p className="body4">Completing signup for: <strong>{email}</strong></p>
              </div>
            )}

          </form>
        </AnimationBox>
      </div>
    </div>
  );
};

export default UserDetails;
