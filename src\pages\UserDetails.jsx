import { useState } from "react";
import AnimationBox from '../components/common/AnimationBox';
import Header from '../components/layout/Header';
import '../styles/UserDetails.css';

const UserDetails = () => {
  const [formData, setFormData] = useState({
    university: '',
    branch: '',
    semester: '',
    rollNumber: ''
  });

  const [errors, setErrors] = useState({});

  // Sample data for dropdowns - you can replace with API calls
  const universities = [
    { value: '', label: 'Select University' },
    { value: 'mumbai_university', label: 'University of Mumbai' },
    { value: 'delhi_university', label: 'University of Delhi' },
    { value: 'pune_university', label: 'University of Pune' },
    { value: 'bangalore_university', label: 'Bangalore University' },
  ];

  const branches = [
    { value: '', label: 'Select Branch' },
    { value: 'computer_science', label: 'Computer Science Engineering' },
    { value: 'information_technology', label: 'Information Technology' },
    { value: 'electronics', label: 'Electronics & Communication' },
    { value: 'mechanical', label: 'Mechanical Engineering' },
    { value: 'civil', label: 'Civil Engineering' },
    { value: 'electrical', label: 'Electrical Engineering' },
  ];

  const semesters = [
    { value: '', label: 'Select Semester' },
    { value: '1', label: '1st Semester' },
    { value: '2', label: '2nd Semester' },
    { value: '3', label: '3rd Semester' },
    { value: '4', label: '4th Semester' },
    { value: '5', label: '5th Semester' },
    { value: '6', label: '6th Semester' },
    { value: '7', label: '7th Semester' },
    { value: '8', label: '8th Semester' }
  ];

  const rollNumbers = [
    { value: '', label: 'Select Roll Number Range' },
    { value: '1-50', label: '1-50' },
    { value: '51-100', label: '51-100' },
    { value: '101-150', label: '101-150' },
    { value: '151-200', label: '151-200' },
    { value: '201-250', label: '201-250' },
    { value: '251-300', label: '251-300' },
    { value: 'custom', label: 'Enter Custom Roll Number' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.university) {
      newErrors.university = 'Please select a university';
    }
    
    if (!formData.branch) {
      newErrors.branch = 'Please select a branch';
    }
    
    if (!formData.semester) {
      newErrors.semester = 'Please select a semester';
    }
    
    if (!formData.rollNumber) {
      newErrors.rollNumber = 'Please select a roll number range';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Here you would typically save to a database or API
      console.log('Saving user details:', formData);
      alert('User details saved successfully!');
      
      // You can add navigation or other success actions here
      // navigate('/dashboard') or similar
    }
  };

  return (
    <div className="user-details-container">
      <Header />
      
      <div className="user-details-content">
        <AnimationBox className="user-details-box">
          <h2 className="h2 user-details-title">User Details</h2>
          
          <form onSubmit={handleSave} className="user-details-form">
            
            {/* University Dropdown */}
            <div className="form-group">
              <label htmlFor="university" className="body2-bold">University</label>
              <select
                id="university"
                value={formData.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={`dropdown-select ${errors.university ? 'error' : ''}`}
              >
                {universities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.university && <div className="error-message body4">{errors.university}</div>}
            </div>

            {/* Branch Dropdown */}
            <div className="form-group">
              <label htmlFor="branch" className="body2-bold">Branch</label>
              <select
                id="branch"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                className={`dropdown-select ${errors.branch ? 'error' : ''}`}
              >
                {branches.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.branch && <div className="error-message body4">{errors.branch}</div>}
            </div>

            {/* Semester Dropdown */}
            <div className="form-group">
              <label htmlFor="semester" className="body2-bold">Semester</label>
              <select
                id="semester"
                value={formData.semester}
                onChange={(e) => handleInputChange('semester', e.target.value)}
                className={`dropdown-select ${errors.semester ? 'error' : ''}`}
              >
                {semesters.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.semester && <div className="error-message body4">{errors.semester}</div>}
            </div>

            {/* Roll Number Dropdown */}
            <div className="form-group">
              <label htmlFor="rollNumber" className="body2-bold">Roll Number</label>
              <select
                id="rollNumber"
                value={formData.rollNumber}
                onChange={(e) => handleInputChange('rollNumber', e.target.value)}
                className={`dropdown-select ${errors.rollNumber ? 'error' : ''}`}
              >
                {rollNumbers.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.rollNumber && <div className="error-message body4">{errors.rollNumber}</div>}
            </div>

            {/* Save Button */}
            <div className="save-button-container">
              <button type="submit" className="save-button body2-bold">
                Save Details
              </button>
            </div>
            
          </form>
        </AnimationBox>
      </div>
    </div>
  );
};

export default UserDetails;
